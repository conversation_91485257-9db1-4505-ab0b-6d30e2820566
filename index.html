<html>
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Manrope%3Awght%40400%3B500%3B700%3B800&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Dev-Portfolio</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />
    <link rel="stylesheet" href="styles.css">

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-[#112222] dark group/design-root overflow-x-hidden" style='font-family: Manrope, "Noto Sans", sans-serif;'>
      <div class="layout-container flex h-full grow flex-col">
        <div class="px-40 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
            <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#234848] px-10 py-3 relative">
              <div class="flex items-center gap-4 text-white">
                <div class="size-4">
                  <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M39.5563 34.1455V13.8546C39.5563 15.708 36.8773 17.3437 32.7927 18.3189C30.2914 18.916 27.263 19.2655 24 19.2655C20.737 19.2655 17.7086 18.916 15.2073 18.3189C11.1227 17.3437 8.44365 15.708 8.44365 13.8546V34.1455C8.44365 35.9988 11.1227 37.6346 15.2073 38.6098C17.7086 39.2069 20.737 39.5564 24 39.5564C27.263 39.5564 30.2914 39.2069 32.7927 38.6098C36.8773 37.6346 39.5563 35.9988 39.5563 34.1455Z"
                      fill="currentColor"
                    ></path>
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M10.4485 13.8519C10.4749 13.9271 10.6203 14.246 11.379 14.7361C12.298 15.3298 13.7492 15.9145 15.6717 16.3735C18.0007 16.9296 20.8712 17.2655 24 17.2655C27.1288 17.2655 29.9993 16.9296 32.3283 16.3735C34.2508 15.9145 35.702 15.3298 36.621 14.7361C37.3796 14.246 37.5251 13.9271 37.5515 13.8519C37.5287 13.7876 37.4333 13.5973 37.0635 13.2931C36.5266 12.8516 35.6288 12.3647 34.343 11.9175C31.79 11.0295 28.1333 10.4437 24 10.4437C19.8667 10.4437 16.2099 11.0295 13.657 11.9175C12.3712 12.3647 11.4734 12.8516 10.9365 13.2931C10.5667 13.5973 10.4713 13.7876 10.4485 13.8519ZM37.5563 18.7877C36.3176 19.3925 34.8502 19.8839 33.2571 20.2642C30.5836 20.9025 27.3973 21.2655 24 21.2655C20.6027 21.2655 17.4164 20.9025 14.7429 20.2642C13.1498 19.8839 11.6824 19.3925 10.4436 18.7877V34.1275C10.4515 34.1545 10.5427 34.4867 11.379 35.027C12.298 35.6207 13.7492 36.2054 15.6717 36.6644C18.0007 37.2205 20.8712 37.5564 24 37.5564C27.1288 37.5564 29.9993 37.2205 32.3283 36.6644C34.2508 36.2054 35.702 35.6207 36.621 35.027C37.4573 34.4867 37.5485 34.1546 37.5563 34.1275V18.7877ZM41.5563 13.8546V34.1455C41.5563 36.1078 40.158 37.5042 38.7915 38.3869C37.3498 39.3182 35.4192 40.0389 33.2571 40.5551C30.5836 41.1934 27.3973 41.5564 24 41.5564C20.6027 41.5564 17.4164 41.1934 14.7429 40.5551C12.5808 40.0389 10.6502 39.3182 9.20848 38.3869C7.84205 37.5042 6.44365 36.1078 6.44365 34.1455L6.44365 13.8546C6.44365 12.2684 7.37223 11.0454 8.39581 10.2036C9.43325 9.3505 10.8137 8.67141 12.343 8.13948C15.4203 7.06909 19.5418 6.44366 24 6.44366C28.4582 6.44366 32.5797 7.06909 35.657 8.13948C37.1863 8.67141 38.5667 9.3505 39.6042 10.2036C40.6278 11.0454 41.5563 12.2684 41.5563 13.8546Z"
                      fill="currentColor"
                    ></path>
                  </svg>
                </div>
                <h2 class="text-white text-lg font-bold leading-tight tracking-[-0.015em]">Denil Anyonyi</h2>
              </div>

              <!-- Desktop Navigation -->
              <div class="desktop-nav flex flex-1 justify-end gap-8">
                <div class="flex items-center gap-9">
                  <a class="nav-link text-white text-sm font-medium leading-normal" href="#home">Home</a>
                  <a class="nav-link text-white text-sm font-medium leading-normal" href="#about">About</a>
                  <a class="nav-link text-white text-sm font-medium leading-normal" href="#resume">Resume</a>
                  <a class="nav-link text-white text-sm font-medium leading-normal" href="#projects">Projects</a>
                  <a class="nav-link text-white text-sm font-medium leading-normal" href="#contact">Contact</a>
                </div>

                <div
                  class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                  style='background-image: url("./assets/profile_picture.jpg");'
                ></div>
              </div>

              <!-- Mobile Hamburger Menu Button -->
              <div class="mobile-nav-container">
                <button
                  id="mobile-menu-btn"
                  class="mobile-menu-btn flex items-center justify-center w-10 h-10 text-white hover:text-[#11e3e3] transition-colors"
                  aria-label="Toggle mobile menu"
                >
                  <svg class="hamburger-icon w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                  </svg>
                  <svg class="close-icon w-6 h-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>

              <!-- Mobile Navigation Menu -->
              <div id="mobile-menu" class="mobile-menu">
                <nav class="mobile-nav">
                  <a class="mobile-nav-link" href="#home">Home</a>
                  <a class="mobile-nav-link" href="#about">About</a>
                  <a class="mobile-nav-link" href="#resume">Resume</a>
                  <a class="mobile-nav-link" href="#projects">Projects</a>
                  <a class="mobile-nav-link" href="#contact">Contact</a>
                </nav>
                <div class="mobile-profile">
                  <div
                    class="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-12 h-12 mx-auto"
                    style='background-image: url("./assets/profile_picture.jpg");'
                  ></div>
                  <p class="text-white text-sm font-medium mt-2 text-center">Denil Anyonyi</p>
                </div>
              </div>
            </header>
            <div id="home" class="@container">
              <div class="@[480px]:p-4">
                <div
                  class="hero-section flex min-h-[480px] flex-col gap-6 bg-cover bg-no-repeat @[480px]:gap-8 @[480px]:rounded-xl items-center justify-center p-4"
                  style='background-image: linear-gradient(rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.7) 100%), url("./assets/hero_section.jpg"); background-position: center 60%;'
                >
                  <div class="flex flex-col gap-2 text-center">
                    <h1
                      class="text-white text-4xl font-black leading-tight tracking-[-0.033em] @[480px]:text-5xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em]"
                    >
                      Software Engineer
                    </h1>
                    <h2 class="text-white text-sm font-normal leading-normal @[480px]:text-base @[480px]:font-normal @[480px]:leading-normal">
                      I'm a software engineer specializing in building exceptional digital experiences. Currently, I'm focused on developing web applications using modern
                      technologies.
                    </h2>
                  </div>
                  <button
                    id="view-projects-btn"
                    class="btn-primary flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 @[480px]:h-12 @[480px]:px-5 bg-[#11e3e3] text-[#112222] text-sm font-bold leading-normal tracking-[0.015em] @[480px]:text-base @[480px]:font-bold @[480px]:leading-normal @[480px]:tracking-[0.015em]"
                  >
                    <span class="truncate">View Projects</span>
                  </button>
                </div>
              </div>
            </div>
            <h2 id="about" class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">About Me</h2>
            <div class="flex p-4 @container">
              <div class="flex w-full flex-col gap-4 @[520px]:flex-row @[520px]:justify-between @[520px]:items-center">
                <div
                    class="bg-center bg-no-repeat aspect-square bg-cover rounded-full min-h-32 w-32"
                    style='background-image: url("./assets/profile_picture.jpg");'
                ></div>

                <div class="flex gap-4">
                  <div class="flex flex-col justify-center">
                    <p class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em]">Denil Anyonyi</p>
                    <p class="text-[#92c9c9] text-base font-normal leading-normal">Software Engineer</p>
                    <p class="text-[#92c9c9] text-base font-normal leading-normal">
                      Denil Anyonyi is a full-stack software developer with two years of experience in the tech industry. He has a solid background in building web applications 
                      using Go (Golang), JavaScript, and Rust, and has worked extensively in agile team environments, where he participates in sprint planning, daily stand-ups, 
                      and iterative delivery cycles. <br><br>

                      One of his recent projects includes building a logistics coordination tool that helps small businesses manage deliveries, track inventory, and streamline 
                      operations through a clean and efficient dashboard. He also contributed to a developer hiring platform, helping a local company better showcase and connect 
                      vetted software talent with potential clients. <br><br>

                      Currently, Denil works at Zone 01 Kisumu, where he collaborates with fellow developers to deliver market-ready applications tailored to client needs. He’s 
                      passionate about data privacy, with a strong focus on keeping user data secure and trustworthy.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <h2 class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Skills</h2>
            <div class="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
              <div class="flex flex-1 gap-3 rounded-lg border border-[#326767] bg-[#193333] p-4 flex-col">
                <div class="text-white" data-icon="Code" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M69.12,94.15,28.5,128l40.62,33.85a8,8,0,1,1-10.24,12.29l-48-40a8,8,0,0,1,0-12.29l48-40a8,8,0,0,1,10.24,12.3Zm176,27.7-48-40a8,8,0,1,0-10.24,12.3L227.5,128l-40.62,33.85a8,8,0,1,0,10.24,12.29l48-40a8,8,0,0,0,0-12.29ZM162.73,32.48a8,8,0,0,0-10.25,4.79l-64,176a8,8,0,0,0,4.79,10.26A8.14,8.14,0,0,0,96,224a8,8,0,0,0,7.52-5.27l64-176A8,8,0,0,0,162.73,32.48Z"
                    ></path>
                  </svg>
                </div>
                <div class="flex flex-col gap-1">
                  <h2 class="text-white text-base font-bold leading-tight">Frontend Development</h2>
                  <p class="text-[#92c9c9] text-sm font-normal leading-normal">Proficient in HTML, CSS, JavaScript, React and Next.js.</p>
                </div>
              </div>
              <div class="flex flex-1 gap-3 rounded-lg border border-[#326767] bg-[#193333] p-4 flex-col">
                <div class="text-white" data-icon="Database" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M128,24C74.17,24,32,48.6,32,80v96c0,31.4,42.17,56,96,56s96-24.6,96-56V80C224,48.6,181.83,24,128,24Zm80,104c0,9.62-7.88,19.43-21.61,26.92C170.93,163.35,150.19,168,128,168s-42.93-4.65-58.39-13.08C55.88,147.43,48,137.62,48,128V111.36c17.06,15,46.23,24.64,80,24.64s62.94-9.68,80-24.64ZM69.61,53.08C85.07,44.65,105.81,40,128,40s42.93,4.65,58.39,13.08C200.12,60.57,208,70.38,208,80s-7.88,19.43-21.61,26.92C170.93,115.35,150.19,120,128,120s-42.93-4.65-58.39-13.08C55.88,99.43,48,89.62,48,80S55.88,60.57,69.61,53.08ZM186.39,202.92C170.93,211.35,150.19,216,128,216s-42.93-4.65-58.39-13.08C55.88,195.43,48,185.62,48,176V159.36c17.06,15,46.23,24.64,80,24.64s62.94-9.68,80-24.64V176C208,185.62,200.12,195.43,186.39,202.92Z"
                    ></path>
                  </svg>
                </div>
                <div class="flex flex-col gap-1">
                  <h2 class="text-white text-base font-bold leading-tight">Backend Development</h2>
                  <p class="text-[#92c9c9] text-sm font-normal leading-normal">Experienced with Go and Django</p>
                </div>
              </div>
              <div class="flex flex-1 gap-3 rounded-lg border border-[#326767] bg-[#193333] p-4 flex-col">
                <div class="text-white" data-icon="Database" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M128,24C74.17,24,32,48.6,32,80v96c0,31.4,42.17,56,96,56s96-24.6,96-56V80C224,48.6,181.83,24,128,24Zm80,104c0,9.62-7.88,19.43-21.61,26.92C170.93,163.35,150.19,168,128,168s-42.93-4.65-58.39-13.08C55.88,147.43,48,137.62,48,128V111.36c17.06,15,46.23,24.64,80,24.64s62.94-9.68,80-24.64ZM69.61,53.08C85.07,44.65,105.81,40,128,40s42.93,4.65,58.39,13.08C200.12,60.57,208,70.38,208,80s-7.88,19.43-21.61,26.92C170.93,115.35,150.19,120,128,120s-42.93-4.65-58.39-13.08C55.88,99.43,48,89.62,48,80S55.88,60.57,69.61,53.08ZM186.39,202.92C170.93,211.35,150.19,216,128,216s-42.93-4.65-58.39-13.08C55.88,195.43,48,185.62,48,176V159.36c17.06,15,46.23,24.64,80,24.64s62.94-9.68,80-24.64V176C208,185.62,200.12,195.43,186.39,202.92Z"
                    ></path>
                  </svg>
                </div>
                <div class="flex flex-col gap-1">
                  <h2 class="text-white text-base font-bold leading-tight">Database Management</h2>
                  <p class="text-[#92c9c9] text-sm font-normal leading-normal">Skilled in SQL and NoSQL databases.</p>
                </div>
              </div>
              <div class="flex flex-1 gap-3 rounded-lg border border-[#326767] bg-[#193333] p-4 flex-col">
                <div class="text-white" data-icon="GitCommit" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M248,120H183.42a56,56,0,0,0-110.84,0H8a8,8,0,0,0,0,16H72.58a56,56,0,0,0,110.84,0H248a8,8,0,0,0,0-16ZM128,168a40,40,0,1,1,40-40A40,40,0,0,1,128,168Z"
                    ></path>
                  </svg>
                </div>
                <div class="flex flex-col gap-1">
                  <h2 class="text-white text-base font-bold leading-tight">Version Control</h2>
                  <p class="text-[#92c9c9] text-sm font-normal leading-normal">Familiar with Git and collaborative workflows.</p>
                </div>
              </div>
            </div>
            <h2 id="resume" class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Resume</h2>
            <div class="flex flex-col gap-6 p-4">
              <div class="flex flex-col gap-4 @container">
                <div class="flex flex-col gap-4 @[520px]:flex-row @[520px]:items-start @[520px]:gap-6">
                  <!-- Work Experience Section -->
                  <div class="flex-1">
                    <div class="flex flex-col gap-3">
                      <h3 class="text-white text-lg font-bold leading-tight">Work Experience</h3>
                      <div class="work-experience-container bg-[#193333] border border-[#326767] rounded-lg p-6">

                        <!-- Current Position -->
                        <div class="experience-item mb-8 pb-6 border-b border-[#234848] last:border-b-0 last:mb-0 last:pb-0">
                          <div class="flex flex-col gap-3">
                            <div class="flex flex-col @[600px]:flex-row @[600px]:items-center @[600px]:justify-between gap-2">
                              <h4 class="text-white text-lg font-bold">Software Developer</h4>
                              <span class="text-[#11e3e3] text-sm font-medium bg-[#112222] px-3 py-1 rounded-full w-fit">2024 - Present</span>
                            </div>
                            <div class="flex items-center gap-2 mb-3">
                              <div class="text-[#11e3e3]" data-icon="Buildings" data-size="16px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
                                  <path d="M240,208H224V96a16,16,0,0,0-16-16H144V32a16,16,0,0,0-16-16H48A16,16,0,0,0,32,32V208H16a8,8,0,0,0,0,16H240a8,8,0,0,0,0-16ZM208,96V208H144V96ZM48,32h80V208H48Z"></path>
                                </svg>
                              </div>
                              <span class="text-[#92c9c9] font-medium">Zone 01 Kisumu</span>
                              <span class="text-[#92c9c9]">•</span>
                              <span class="text-[#92c9c9]">Kisumu, Kenya</span>
                            </div>
                            <div class="space-y-3">
                              <div class="flex items-start gap-3">
                                <div class="text-[#11e3e3] mt-1" data-icon="CheckCircle" data-size="16px" data-weight="regular">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
                                    <path d="M173.66,98.34a8,8,0,0,1,0,11.32l-56,56a8,8,0,0,1-11.32,0l-24-24a8,8,0,0,1,11.32-11.32L112,148.69l50.34-50.35A8,8,0,0,1,173.66,98.34ZM232,128A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z"></path>
                                  </svg>
                                </div>
                                <p class="text-[#92c9c9] text-sm leading-relaxed">Collaborate with cross-functional development teams to design, develop, and deliver market-ready web applications using modern technologies and agile methodologies</p>
                              </div>
                              <div class="flex items-start gap-3">
                                <div class="text-[#11e3e3] mt-1" data-icon="CheckCircle" data-size="16px" data-weight="regular">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
                                    <path d="M173.66,98.34a8,8,0,0,1,0,11.32l-56,56a8,8,0,0,1-11.32,0l-24-24a8,8,0,0,1,11.32-11.32L112,148.69l50.34-50.35A8,8,0,0,1,173.66,98.34ZM232,128A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z"></path>
                                  </svg>
                                </div>
                                <p class="text-[#92c9c9] text-sm leading-relaxed">Built and deployed a comprehensive logistics coordination tool that helps small businesses manage deliveries, track inventory, and streamline operations through an intuitive dashboard interface</p>
                              </div>
                              <div class="flex items-start gap-3">
                                <div class="text-[#11e3e3] mt-1" data-icon="CheckCircle" data-size="16px" data-weight="regular">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
                                    <path d="M173.66,98.34a8,8,0,0,1,0,11.32l-56,56a8,8,0,0,1-11.32,0l-24-24a8,8,0,0,1,11.32-11.32L112,148.69l50.34-50.35A8,8,0,0,1,173.66,98.34ZM232,128A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z"></path>
                                  </svg>
                                </div>
                                <p class="text-[#92c9c9] text-sm leading-relaxed">Contributed to the development of a developer hiring platform that connects vetted software talent with potential clients, improving the recruitment process for local companies</p>
                              </div>
                              <div class="flex items-start gap-3">
                                <div class="text-[#11e3e3] mt-1" data-icon="CheckCircle" data-size="16px" data-weight="regular">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
                                    <path d="M173.66,98.34a8,8,0,0,1,0,11.32l-56,56a8,8,0,0,1-11.32,0l-24-24a8,8,0,0,1,11.32-11.32L112,148.69l50.34-50.35A8,8,0,0,1,173.66,98.34ZM232,128A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z"></path>
                                  </svg>
                                </div>
                                <p class="text-[#92c9c9] text-sm leading-relaxed">Actively participate in sprint planning, daily stand-ups, code reviews, and iterative delivery cycles, ensuring high-quality code and timely project completion</p>
                              </div>
                              <div class="flex items-start gap-3">
                                <div class="text-[#11e3e3] mt-1" data-icon="CheckCircle" data-size="16px" data-weight="regular">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
                                    <path d="M173.66,98.34a8,8,0,0,1,0,11.32l-56,56a8,8,0,0,1-11.32,0l-24-24a8,8,0,0,1,11.32-11.32L112,148.69l50.34-50.35A8,8,0,0,1,173.66,98.34ZM232,128A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z"></path>
                                  </svg>
                                </div>
                                <p class="text-[#92c9c9] text-sm leading-relaxed">Implement robust data privacy and security measures in all applications, ensuring user data protection and compliance with industry standards</p>
                              </div>
                            </div>
                            <div class="flex flex-wrap gap-2 mt-4">
                              <span class="bg-[#234848] text-[#11e3e3] text-xs px-2 py-1 rounded">Go</span>
                              <span class="bg-[#234848] text-[#11e3e3] text-xs px-2 py-1 rounded">JavaScript</span>
                              <span class="bg-[#234848] text-[#11e3e3] text-xs px-2 py-1 rounded">React</span>
                              <span class="bg-[#234848] text-[#11e3e3] text-xs px-2 py-1 rounded">PostgreSQL</span>
                              <span class="bg-[#234848] text-[#11e3e3] text-xs px-2 py-1 rounded">Agile/Scrum</span>
                            </div>
                          </div>
                        </div>

                        <!-- Education/Training -->
                        <div class="experience-item">
                          <div class="flex flex-col gap-3">
                            <div class="flex flex-col @[600px]:flex-row @[600px]:items-center @[600px]:justify-between gap-2">
                              <h4 class="text-white text-lg font-bold">Software Development Program</h4>
                              <span class="text-[#11e3e3] text-sm font-medium bg-[#112222] px-3 py-1 rounded-full w-fit">2024</span>
                            </div>
                            <div class="flex items-center gap-2 mb-3">
                              <div class="text-[#11e3e3]" data-icon="GraduationCap" data-size="16px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
                                  <path d="M251.76,88.94l-120-64a8,8,0,0,0-7.52,0l-120,64a8,8,0,0,0,0,14.12L32,117.87v48.42a15.91,15.91,0,0,0,4.06,10.65C49.16,191.53,78.51,216,128,216s78.84-24.47,91.94-39.06A15.91,15.91,0,0,0,224,166.29V117.87l27.76-14.81a8,8,0,0,0,0-14.12ZM128,200c-43.27,0-68.72-21.14-80-33.71V126.4l76.24,40.66a8,8,0,0,0,7.52,0L208,126.4v39.89C196.72,178.86,171.27,200,128,200Zm0-33.87L57.05,96,128,25.87,198.95,96Z"></path>
                                </svg>
                              </div>
                              <span class="text-[#92c9c9] font-medium">Zone 01 Kisumu</span>
                              <span class="text-[#92c9c9]">•</span>
                              <span class="text-[#92c9c9]">Kisumu, Kenya</span>
                            </div>
                            <div class="space-y-3">
                              <div class="flex items-start gap-3">
                                <div class="text-[#11e3e3] mt-1" data-icon="CheckCircle" data-size="16px" data-weight="regular">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
                                    <path d="M173.66,98.34a8,8,0,0,1,0,11.32l-56,56a8,8,0,0,1-11.32,0l-24-24a8,8,0,0,1,11.32-11.32L112,148.69l50.34-50.35A8,8,0,0,1,173.66,98.34ZM232,128A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z"></path>
                                  </svg>
                                </div>
                                <p class="text-[#92c9c9] text-sm leading-relaxed">Completed intensive software development program focusing on modern web technologies, algorithms, and software engineering best practices</p>
                              </div>
                              <div class="flex items-start gap-3">
                                <div class="text-[#11e3e3] mt-1" data-icon="CheckCircle" data-size="16px" data-weight="regular">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
                                    <path d="M173.66,98.34a8,8,0,0,1,0,11.32l-56,56a8,8,0,0,1-11.32,0l-24-24a8,8,0,0,1,11.32-11.32L112,148.69l50.34-50.35A8,8,0,0,1,173.66,98.34ZM232,128A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z"></path>
                                  </svg>
                                </div>
                                <p class="text-[#92c9c9] text-sm leading-relaxed">Gained hands-on experience in full-stack development, database design, and collaborative software development methodologies</p>
                              </div>
                            </div>
                            <div class="flex flex-wrap gap-2 mt-4">
                              <span class="bg-[#234848] text-[#11e3e3] text-xs px-2 py-1 rounded">Full-Stack Development</span>
                              <span class="bg-[#234848] text-[#11e3e3] text-xs px-2 py-1 rounded">Algorithms</span>
                              <span class="bg-[#234848] text-[#11e3e3] text-xs px-2 py-1 rounded">Software Engineering</span>
                            </div>
                          </div>
                        </div>

                      </div>
                    </div>
                  </div>

                  <!-- Download Section -->
                  <div class="@[520px]:w-64">
                    <div class="flex flex-col gap-4">
                      <h3 class="text-white text-lg font-bold leading-tight">Download Resume</h3>
                      <div class="bg-[#193333] border border-[#326767] rounded-lg p-4">
                        <div class="flex flex-col gap-4">
                          <div class="text-center">
                            <div class="text-[#92c9c9] mb-2" data-icon="FileText" data-size="48px" data-weight="regular">
                              <svg xmlns="http://www.w3.org/2000/svg" width="48px" height="48px" fill="currentColor" viewBox="0 0 256 256" class="mx-auto">
                                <path d="M213.66,82.34l-56-56A8,8,0,0,0,152,24H56A16,16,0,0,0,40,40V216a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V88A8,8,0,0,0,213.66,82.34ZM160,51.31,188.69,80H160ZM200,216H56V40h88V88a8,8,0,0,0,8,8h48V216Z"></path>
                              </svg>
                            </div>
                            <p class="text-[#92c9c9] text-sm mb-4">Get the full version of my resume in HTML format</p>
                          </div>

                          <button
                            id="download-resume-btn"
                            class="flex items-center justify-center gap-2 cursor-pointer overflow-hidden rounded-full h-12 px-4 bg-[#11e3e3] text-[#112222] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#0dd4d4] transition-colors"
                          >
                            <div class="text-[#112222]" data-icon="Download" data-size="20px" data-weight="regular">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                                <path d="M224,152v56a16,16,0,0,1-16,16H48a16,16,0,0,1-16-16V152a8,8,0,0,1,16,0v56H208V152a8,8,0,0,1,16,0ZM101.66,133.66,120,152V40a8,8,0,0,1,16,0V152l18.34-18.34a8,8,0,0,1,11.32,11.32l-32,32a8,8,0,0,1-11.32,0l-32-32a8,8,0,0,1,11.32-11.32Z"></path>
                              </svg>
                            </div>
                            <span class="truncate">View Resume</span>
                          </button>

                          <div class="text-center">
                            <p class="text-[#92c9c9] text-xs">Last updated: January 2025</p>
                          </div>
                        </div>
                      </div>

                      <!-- Quick Actions -->
                      <div class="bg-[#193333] border border-[#326767] rounded-lg p-4">
                        <h4 class="text-white text-sm font-bold mb-3">Quick Actions</h4>
                        <div class="flex flex-col gap-2">
                          <button
                            id="print-resume-btn"
                            class="flex items-center gap-2 text-[#92c9c9] hover:text-white text-sm p-2 rounded hover:bg-[#234848] transition-colors"
                          >
                            <div data-icon="Printer" data-size="16px" data-weight="regular">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
                                <path d="M210,64H182V40a16,16,0,0,0-16-16H90A16,16,0,0,0,74,40V64H46A22,22,0,0,0,24,86v80a22,22,0,0,0,22,22H74v24a16,16,0,0,0,16,16h76a16,16,0,0,0,16-16V188h28a22,22,0,0,0,22-22V86A22,22,0,0,0,210,64ZM90,40h76V64H90ZM166,212H90V176h76Zm44-46a6,6,0,0,1-6,6H182V160a16,16,0,0,0-16-16H90a16,16,0,0,0-16,16v12H46a6,6,0,0,1-6-6V86a6,6,0,0,1,6-6H210a6,6,0,0,1,6,6Z"></path>
                              </svg>
                            </div>
                            <span>Print Resume</span>
                          </button>
                          <button
                            id="share-resume-btn"
                            class="flex items-center gap-2 text-[#92c9c9] hover:text-white text-sm p-2 rounded hover:bg-[#234848] transition-colors"
                          >
                            <div data-icon="Share" data-size="16px" data-weight="regular">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16px" height="16px" fill="currentColor" viewBox="0 0 256 256">
                                <path d="M229.66,109.66l-48,48a8,8,0,0,1-11.32-11.32L204.69,112H165a88,88,0,0,0-85.23,66,8,8,0,0,1-15.5-4A103.94,103.94,0,0,1,165,96h39.71L170.34,61.66a8,8,0,0,1,11.32-11.32l48,48A8,8,0,0,1,229.66,109.66Z"></path>
                              </svg>
                            </div>
                            <span>Share Link</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <h2 id="projects" class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Projects</h2>
            <div id="projects-loading" class="flex justify-center items-center p-8">
              <div class="text-[#92c9c9] text-base">Loading projects...</div>
            </div>
            <div id="projects-container" class="grid grid-cols-[repeat(auto-fit,minmax(300px,1fr))] gap-4 p-4" style="display: none;"></div>
            <div id="projects-error" class="text-center p-8" style="display: none;">
              <p class="text-[#92c9c9] text-base">Unable to load projects. Please try again later.</p>
            </div>
            <h2 id="contact" class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Contact</h2>
            <form id="contact-form">
              <div class="flex max-w-[480px] flex-wrap items-end gap-2 px-4 py-3">
                <label class="flex flex-col min-w-40 flex-1">
                  <p class="text-white text-base font-medium leading-normal pb-2">Name</p>
                  <input
                    id="name-input"
                    placeholder="Your Name"
                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-white focus:outline-0 focus:ring-0 border border-[#326767] bg-[#193333] focus:border-[#326767] h-14 placeholder:text-[#92c9c9] p-[15px] text-base font-normal leading-normal"
                    value=""
                  />
                </label>
              </div>
              <p id="name-error" class="validation-message text-[#92c9c9] text-sm font-normal leading-normal pb-3 pt-1 px-4">Please enter your name.</p>
              <div class="flex max-w-[480px] flex-wrap items-end gap-2 px-4 py-3">
                <label class="flex flex-col min-w-40 flex-1">
                  <p class="text-white text-base font-medium leading-normal pb-2">Email</p>
                  <input
                    id="email-input"
                    type="email"
                    placeholder="Your Email"
                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-white focus:outline-0 focus:ring-0 border border-[#326767] bg-[#193333] focus:border-[#326767] h-14 placeholder:text-[#92c9c9] p-[15px] text-base font-normal leading-normal"
                    value=""
                  />
                </label>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

              </div>
              <p id="email-error" class="validation-message text-[#92c9c9] text-sm font-normal leading-normal pb-3 pt-1 px-4">Please enter a valid email address.</p>
              <div class="flex max-w-[480px] flex-wrap items-end gap-2 px-4 py-3">
                <label class="flex flex-col min-w-40 flex-1">
                  <p class="text-white text-base font-medium leading-normal pb-2">Message</p>
                  <textarea
                    id="message-input"
                    placeholder="Your Message"
                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-white focus:outline-0 focus:ring-0 border border-[#326767] bg-[#193333] focus:border-[#326767] min-h-36 placeholder:text-[#92c9c9] p-[15px] text-base font-normal leading-normal"
                  ></textarea>
                </label>
              </div>
              <p id="message-error" class="validation-message text-[#92c9c9] text-sm font-normal leading-normal pb-3 pt-1 px-4">Please enter your message.</p>
              <div class="flex px-4 py-3 justify-start">
                <button
                  id="submit-btn"
                  type="button"
                  class="btn-primary flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#11e3e3] text-[#112222] text-sm font-bold leading-normal tracking-[0.015em]"
                >
                  <span class="truncate">Submit</span>
                </button>
              </div>
            </form>
            <p id="success-message" class="success-message text-[#92c9c9] text-sm font-normal leading-normal pb-3 pt-1 px-4">Message sent successfully!</p>
            <div class="flex justify-end overflow-hidden px-5 pb-5">
              <button
                id="back-to-top"
                class="back-to-top flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-14 px-5 bg-[#11e3e3] text-[#112222] text-base font-bold leading-normal tracking-[0.015em] min-w-0 gap-2 pl-4 pr-6"
              >
                <div class="text-[#112222]" data-icon="ArrowUp" data-size="24px" data-weight="regular">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M205.66,117.66a8,8,0,0,1-11.32,0L136,59.31V216a8,8,0,0,1-16,0V59.31L61.66,117.66a8,8,0,0,1-11.32-11.32l72-72a8,8,0,0,1,11.32,0l72,72A8,8,0,0,1,205.66,117.66Z"
                    ></path>
                  </svg>
                </div>
                <span class="truncate">Back to Top</span>
              </button>
            </div>
            <footer class="flex flex-col gap-6 px-5 py-10 text-center @container">
              <div class="flex flex-wrap justify-center gap-4">
                <a href="https://twitter.com/denil_dev" target="_blank">
                  <div class="text-[#92c9c9]" data-icon="TwitterLogo" data-size="24px" data-weight="regular">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                      <path
                        d="M247.39,68.94A8,8,0,0,0,240,64H209.57A48.66,48.66,0,0,0,168.1,40a46.91,46.91,0,0,0-33.75,13.7A47.9,47.9,0,0,0,120,88v6.09C79.74,83.47,46.81,50.72,46.46,50.37a8,8,0,0,0-13.65,4.92c-4.31,47.79,9.57,79.77,22,98.18a110.93,110.93,0,0,0,21.88,24.2c-15.23,17.53-39.21,26.74-39.47,26.84a8,8,0,0,0-3.85,11.93c.75,1.12,3.75,5.05,11.08,8.72C53.51,229.7,65.48,232,80,232c70.67,0,129.72-54.42,135.75-124.44l29.91-29.9A8,8,0,0,0,247.39,68.94Zm-45,29.41a8,8,0,0,0-2.32,5.14C196,166.58,143.28,216,80,216c-10.56,0-18-1.4-23.22-3.08,11.51-6.25,27.56-17,37.88-32.48A8,8,0,0,0,92,169.08c-.47-.27-43.91-26.34-44-96,16,13,45.25,33.17,78.67,38.79A8,8,0,0,0,136,104V88a32,32,0,0,1,9.6-22.92A30.94,30.94,0,0,1,167.9,56c12.66.16,24.49,7.88,29.44,19.21A8,8,0,0,0,204.67,80h16Z"
                      ></path>
                    </svg>
                  </div>
                </a>
                <a href="https://github.com/denilany" target="_blank">
                  <div class="text-[#92c9c9]" data-icon="GithubLogo" data-size="24px" data-weight="regular">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                      <path
                        d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68ZM200,112a40,40,0,0,1-40,40H112a40,40,0,0,1-40-40v-8a41.74,41.74,0,0,1,6.9-22.48A8,8,0,0,0,80,73.83a43.81,43.81,0,0,1,.79-33.58,43.88,43.88,0,0,1,32.32,20.06A8,8,0,0,0,119.82,64h32.35a8,8,0,0,0,6.74-3.69,43.87,43.87,0,0,1,32.32-20.06A43.81,43.81,0,0,1,192,73.83a8.09,8.09,0,0,0,1,7.65A41.72,41.72,0,0,1,200,104Z"
                      ></path>
                    </svg>
                  </div>
                </a>
                <a href="https://www.linkedin.com/in/denil-anyonyi/">
                  <div class="text-[#92c9c9]" data-icon="LinkedinLogo" data-size="24px" data-weight="regular">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                      <path
                        d="M216,24H40A16,16,0,0,0,24,40V216a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V40A16,16,0,0,0,216,24Zm0,192H40V40H216V216ZM96,112v64a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm88,28v36a8,8,0,0,1-16,0V140a20,20,0,0,0-40,0v36a8,8,0,0,1-16,0V112a8,8,0,0,1,15.79-1.78A36,36,0,0,1,184,140ZM100,84A12,12,0,1,1,88,72,12,12,0,0,1,100,84Z"
                      ></path>
                    </svg>
                  </div>
                </a>
              </div>
              <p class="text-[#92c9c9] text-base font-normal leading-normal">© <span id="footer-year">2025</span> Denil. All rights reserved.</p>
            </footer>
          </div>
        </div>
      </div>
    </div>
    <script src="script.js"></script>
  </body>
</html>
